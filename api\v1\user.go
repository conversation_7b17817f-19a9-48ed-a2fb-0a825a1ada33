package v1

import (
	"time"
	"github.com/shopspring/decimal"
)

// UserQueryReq 用户查询请求
type UserQueryReq struct {
	Current  int    `form:"current" binding:"omitempty,min=1" validate:"omitempty,min=1"`                // 当前页，默认1
	Size     int    `form:"size" binding:"omitempty,min=1,max=100" validate:"omitempty,min=1,max=100"`   // 每页大小，默认10
	Keyword  string `form:"keyword" binding:"omitempty,max=100" validate:"omitempty,max=100"`            // 关键词搜索（用户名、邮箱）
	Role     string `form:"role" binding:"omitempty,max=20" validate:"omitempty,max=20"`                 // 角色筛选
	Status   *int8  `form:"status" binding:"omitempty,min=0,max=1" validate:"omitempty,min=0,max=1"`     // 状态筛选
}

// UserCreateReq 创建用户请求
type UserCreateReq struct {
	Username  string `json:"username" binding:"required,min=3,max=50" validate:"required,min=3,max=50"`   // 用户名
	Email     string `json:"email" binding:"required,email,max=100" validate:"required,email,max=100"`    // 邮箱
	Phone     string `json:"phone" binding:"omitempty,max=20" validate:"omitempty,max=20"`                // 手机号
	Role      string `json:"role" binding:"required,max=20" validate:"required,max=20"`                   // 角色
	Status    *int8  `json:"status" binding:"omitempty,min=0,max=1" validate:"omitempty,min=0,max=1"`     // 状态
	AvatarUrl string `json:"avatarUrl" binding:"omitempty,max=255" validate:"omitempty,max=255,url"`      // 头像URL
	Password  string `json:"password" binding:"required,min=6,max=100" validate:"required,min=6,max=100"` // 密码
	Openid    string `json:"openid" binding:"omitempty,max=100" validate:"omitempty,max=100"`             // 微信openid
}

// UserUpdateReq 更新用户请求
type UserUpdateReq struct {
	ID           int64     `json:"-"`                                                                          // 用户ID（从URL路径获取，不从JSON解析）
	Username     string    `json:"username" binding:"omitempty,min=3,max=50" validate:"omitempty,min=3,max=50"`   // 用户名
	Email        string    `json:"email" binding:"omitempty,email,max=100" validate:"omitempty,email,max=100"`    // 邮箱
	Phone        string    `json:"phone" binding:"omitempty,max=20" validate:"omitempty,max=20"`                  // 手机号
	AvatarUrl    string    `json:"avatarUrl" binding:"omitempty,max=255" validate:"omitempty,max=255,url"`        // 头像URL
	Role         string    `json:"role" binding:"omitempty,max=20" validate:"omitempty,max=20"`                   // 角色
	Status       *int8     `json:"status" binding:"omitempty,min=0,max=1" validate:"omitempty,min=0,max=1"`       // 状态
	RegisterTime time.Time `json:"registerTime" binding:"omitempty"`                                               // 注册时间（与Java版本保持一致）
}

// UserInfoResponse 用户信息响应
type UserInfoResponse struct {
	ID         int64     `json:"id"`                   // 用户ID
	Username   string    `json:"username"`             // 用户名
	Role       string    `json:"role"`                 // 角色
	Email      string    `json:"email"`                // 邮箱
	Status     int8      `json:"status"`               // 状态
	CreateTime time.Time `json:"createTime"`           // 创建时间
	AvatarUrl  string    `json:"avatarUrl,omitempty"`  // 用户头像URL
	Phone      string    `json:"phone,omitempty"`      // 手机号（可选）
}

// UserListResponse 用户列表响应（用于分页）
type UserListResponse struct {
	Total   int64               `json:"total"`   // 总记录数
	Records []*UserInfoResponse `json:"records"` // 用户列表
	Current int                 `json:"current"` // 当前页码
	Size    int                 `json:"size"`    // 每页大小
}

// AvatarUploadReq 头像上传请求
type AvatarUploadReq struct {
	UserID int64 `json:"userId" binding:"required,min=1" validate:"required,min=1"` // 用户ID
}

// AvatarResponse 头像响应
type AvatarResponse struct {
	UploadUrl string `json:"uploadUrl,omitempty"` // 上传URL
	FileName  string `json:"fileName,omitempty"`  // 文件名
	AvatarUrl string `json:"avatarUrl,omitempty"` // 头像URL
}

// UserNutritionGoalCreateReq 创建用户营养目标请求
type UserNutritionGoalCreateReq struct {
	UserID        int64            `json:"userId" binding:"required,min=1" validate:"required,min=1"`                    // 用户ID
	CalorieTarget *int             `json:"calorieTarget" binding:"omitempty,min=0" validate:"omitempty,min=0"`           // 每日热量目标 (千卡)
	WeightTarget  *decimal.Decimal `json:"weightTarget" binding:"omitempty,min=0" validate:"omitempty,min=0"`            // 目标体重 (kg)
	ProteinTarget *int             `json:"proteinTarget" binding:"omitempty,min=0" validate:"omitempty,min=0"`           // 蛋白质目标 (g)
	CarbsTarget   *int             `json:"carbsTarget" binding:"omitempty,min=0" validate:"omitempty,min=0"`             // 碳水化合物目标 (g)
	FatTarget     *int             `json:"fatTarget" binding:"omitempty,min=0" validate:"omitempty,min=0"`               // 脂肪目标 (g)
	IsVegetarian  bool             `json:"isVegetarian"`                                                                  // 是否是素食主义者
	IsLowCarb     bool             `json:"isLowCarb"`                                                                     // 是否低碳水饮食
	IsHighProtein bool             `json:"isHighProtein"`                                                                 // 是否高蛋白饮食
	IsGlutenFree  bool             `json:"isGlutenFree"`                                                                  // 是否无麸质饮食
	IsLowSodium   bool             `json:"isLowSodium"`                                                                   // 是否低钠饮食
}

// UserNutritionGoalUpdateReq 更新用户营养目标请求
type UserNutritionGoalUpdateReq struct {
	ID            int64            `json:"-"`                                                                             // 营养目标ID（从URL路径获取，不从JSON解析）
	CalorieTarget *int             `json:"calorieTarget" binding:"omitempty,min=0" validate:"omitempty,min=0"`           // 每日热量目标 (千卡)
	WeightTarget  *decimal.Decimal `json:"weightTarget" binding:"omitempty,min=0" validate:"omitempty,min=0"`            // 目标体重 (kg)
	ProteinTarget *int             `json:"proteinTarget" binding:"omitempty,min=0" validate:"omitempty,min=0"`           // 蛋白质目标 (g)
	CarbsTarget   *int             `json:"carbsTarget" binding:"omitempty,min=0" validate:"omitempty,min=0"`             // 碳水化合物目标 (g)
	FatTarget     *int             `json:"fatTarget" binding:"omitempty,min=0" validate:"omitempty,min=0"`               // 脂肪目标 (g)
	IsVegetarian  bool             `json:"isVegetarian"`                                                                  // 是否是素食主义者
	IsLowCarb     bool             `json:"isLowCarb"`                                                                     // 是否低碳水饮食
	IsHighProtein bool             `json:"isHighProtein"`                                                                 // 是否高蛋白饮食
	IsGlutenFree  bool             `json:"isGlutenFree"`                                                                  // 是否无麸质饮食
	IsLowSodium   bool             `json:"isLowSodium"`                                                                   // 是否低钠饮食
}

// UserNutritionGoalResponse 用户营养目标响应
type UserNutritionGoalResponse struct {
	ID            int64            `json:"id"`                   // 营养目标ID
	UserID        int64            `json:"userId"`               // 用户ID
	CalorieTarget *int             `json:"calorieTarget"`        // 每日热量目标 (千卡)
	WeightTarget  *decimal.Decimal `json:"weightTarget"`         // 目标体重 (kg)
	ProteinTarget *int             `json:"proteinTarget"`        // 蛋白质目标 (g)
	CarbsTarget   *int             `json:"carbsTarget"`          // 碳水化合物目标 (g)
	FatTarget     *int             `json:"fatTarget"`            // 脂肪目标 (g)
	IsVegetarian  bool             `json:"isVegetarian"`         // 是否是素食主义者
	IsLowCarb     bool             `json:"isLowCarb"`            // 是否低碳水饮食
	IsHighProtein bool             `json:"isHighProtein"`        // 是否高蛋白饮食
	IsGlutenFree  bool             `json:"isGlutenFree"`         // 是否无麸质饮食
	IsLowSodium   bool             `json:"isLowSodium"`          // 是否低钠饮食
	CreatedAt     *time.Time       `json:"createdAt"`            // 创建时间
	UpdatedAt     *time.Time       `json:"updatedAt"`            // 更新时间
}

// PasswordUpdateReq 密码更新请求
type PasswordUpdateReq struct {
	OldPassword string `json:"oldPassword" binding:"required,min=6,max=100" validate:"required,min=6,max=100"` // 旧密码
	NewPassword string `json:"newPassword" binding:"required,min=6,max=100" validate:"required,min=6,max=100"` // 新密码
}

// UserStatusUpdateReq 用户状态更新请求
type UserStatusUpdateReq struct {
	Status int8 `json:"status" binding:"required,min=0,max=1" validate:"required,min=0,max=1"` // 状态（0-禁用，1-启用）
}

// NutritionGoalRequestReq 营养目标请求（与Java项目NutritionGoalRequestDTO对应）
type NutritionGoalRequestReq struct {
	CalorieTarget *int             `json:"calorieTarget" binding:"omitempty,min=0" validate:"omitempty,min=0"`           // 每日热量目标 (千卡)
	WeightTarget  *decimal.Decimal `json:"weightTarget" binding:"omitempty,min=0" validate:"omitempty,min=0"`            // 目标体重 (kg)
	ProteinTarget *int             `json:"proteinTarget" binding:"omitempty,min=0" validate:"omitempty,min=0"`           // 蛋白质目标 (g)
	CarbsTarget   *int             `json:"carbsTarget" binding:"omitempty,min=0" validate:"omitempty,min=0"`             // 碳水化合物目标 (g)
	FatTarget     *int             `json:"fatTarget" binding:"omitempty,min=0" validate:"omitempty,min=0"`               // 脂肪目标 (g)
	IsVegetarian  bool             `json:"isVegetarian"`                                                                  // 是否是素食主义者
	IsLowCarb     bool             `json:"isLowCarb"`                                                                     // 是否低碳水饮食
	IsHighProtein bool             `json:"isHighProtein"`                                                                 // 是否高蛋白饮食
	IsGlutenFree  bool             `json:"isGlutenFree"`                                                                  // 是否无麸质饮食
	IsLowSodium   bool             `json:"isLowSodium"`                                                                   // 是否低钠饮食
}
