package controllers

import (
	"github.com/gin-gonic/gin"
	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/common/response"
	"shikeyinxiang/internal/consts"
	"shikeyinxiang/internal/middleware"
	"shikeyinxiang/internal/service"
)

// UserController 用户端控制器，处理普通用户相关的HTTP请求
type UserController struct {
	userSvc             service.IUserService
	nutritionGoalSvc    service.IUserNutritionGoalService
}

// NewUserController 创建一个新的 UserController 实例
func NewUserController(userSvc service.IUserService, nutritionGoalSvc service.IUserNutritionGoalService) *UserController {
	return &UserController{
		userSvc:          userSvc,
		nutritionGoalSvc: nutritionGoalSvc,
	}
}

// bindJSONAndValidate 统一处理JSON绑定和验证
func (uc *UserController) bindJSONAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindJSON(req); err != nil {
		response.Error(c, consts.CodeParameterError)
		return false
	}
	return true
}

// getCurrentUserID 获取当前用户ID
func (uc *UserController) getCurrentUserID(c *gin.Context) (int64, bool) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		response.Error(c, consts.CodeUnauthorized)
		return 0, false
	}
	return userID, true
}

// GetUserInfo 获取当前用户信息
// GET /user/info
func (uc *UserController) GetUserInfo(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := uc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 调用服务层获取用户信息
	res, err := uc.userSvc.GetUserByID(c.Request.Context(), userID)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// UpdateUserInfo 更新用户信息
// PUT /user/update
func (uc *UserController) UpdateUserInfo(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := uc.getCurrentUserID(c)
	if !ok {
		return
	}

	var req v1.UserUpdateReq
	if !uc.bindJSONAndValidate(c, &req) {
		return
	}

	// 设置用户ID
	req.ID = userID

	// 调用服务层更新用户信息
	res, err := uc.userSvc.UpdateUser(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	// 返回更新成功的标志（与Java版本保持一致）
	response.Success(c, true)
}

// GetAvatarUrl 获取用户头像URL
// GET /user/avatar
func (uc *UserController) GetAvatarUrl(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := uc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 调用服务层生成预签名下载URL
	res, err := uc.userSvc.GenerateAvatarDownloadURL(c.Request.Context(), userID)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// GenerateAvatarUploadUrl 生成头像上传URL
// POST /user/avatar/upload-url
func (uc *UserController) GenerateAvatarUploadUrl(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := uc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 获取查询参数中的contentType
	contentType := c.Query("contentType")
	if contentType == "" {
		response.Error(c, consts.CodeParameterError)
		return
	}

	// 调用服务层生成预签名上传URL
	res, err := uc.userSvc.GenerateAvatarUploadURL(c.Request.Context(), userID, contentType)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// UploadAvatar 上传头像（代理上传，解决CORS问题）
// POST /user/avatar
func (uc *UserController) UploadAvatar(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := uc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		response.Error(c, consts.CodeParameterError)
		return
	}
	defer file.Close()

	// 验证文件大小（10MB限制）
	if header.Size > 10*1024*1024 {
		response.Error(c, consts.CodeParameterError)
		return
	}

	// 获取文件类型
	contentType := header.Header.Get("Content-Type")
	if contentType == "" {
		// 根据文件扩展名推断Content-Type
		contentType = "image/jpeg" // 默认值
	}

	// 验证文件类型
	validTypes := []string{"image/jpeg", "image/png", "image/gif", "image/webp"}
	isValid := false
	for _, validType := range validTypes {
		if contentType == validType {
			isValid = true
			break
		}
	}
	if !isValid {
		response.Error(c, consts.CodeParameterError)
		return
	}

	// 生成上传URL和文件名
	avatarRes, err := uc.userSvc.GenerateAvatarUploadURL(c.Request.Context(), userID, contentType)
	if err != nil {
		c.Error(err)
		return
	}

	// 这里可以选择两种方式：
	// 1. 返回预签名URL让前端直接上传（推荐，避免服务器带宽消耗）
	// 2. 代理上传（需要消耗服务器带宽，但可以更好地控制上传过程）

	// 方式1：返回预签名URL（推荐）
	response.Success(c, gin.H{
		"uploadUrl": avatarRes.UploadUrl,
		"fileName":  avatarRes.FileName,
		"message":   "请使用uploadUrl直接上传文件，上传完成后调用PUT /user/avatar更新头像URL",
	})
}

// UpdateAvatar 更新用户头像URL（在文件上传完成后调用）
// PUT /user/avatar
func (uc *UserController) UpdateAvatar(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := uc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 绑定请求参数
	var req struct {
		AvatarUrl string `json:"avatarUrl" binding:"required,url"`
	}
	if !uc.bindJSONAndValidate(c, &req) {
		return
	}

	// 调用服务层更新头像URL
	err := uc.userSvc.UpdateUserAvatar(c.Request.Context(), userID, req.AvatarUrl)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, gin.H{"message": "头像更新成功"})
}

// UploadAvatarProxy 代理上传头像（服务器端代理上传到R2）
// POST /user/avatar/proxy
func (uc *UserController) UploadAvatarProxy(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := uc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		response.Error(c, consts.CodeParameterError)
		return
	}
	defer file.Close()

	// 验证文件大小（10MB限制）
	if header.Size > 10*1024*1024 {
		response.Error(c, consts.CodeParameterError)
		return
	}

	// 获取文件类型
	contentType := header.Header.Get("Content-Type")
	if contentType == "" {
		contentType = "image/jpeg" // 默认值
	}

	// 验证文件类型
	validTypes := []string{"image/jpeg", "image/png", "image/gif", "image/webp"}
	isValid := false
	for _, validType := range validTypes {
		if contentType == validType {
			isValid = true
			break
		}
	}
	if !isValid {
		response.Error(c, consts.CodeParameterError)
		return
	}

	// 生成文件名
	avatarRes, err := uc.userSvc.GenerateAvatarUploadURL(c.Request.Context(), userID, contentType)
	if err != nil {
		c.Error(err)
		return
	}

	// 这里需要调用文件服务的代理上传功能
	// 由于当前接口设计限制，我们暂时返回预签名URL
	// 在实际项目中，可以添加一个专门的代理上传方法
	response.Success(c, gin.H{
		"uploadUrl": avatarRes.UploadUrl,
		"fileName":  avatarRes.FileName,
		"message":   "代理上传功能需要进一步实现，当前返回预签名URL",
	})
}

// ChangePassword 修改密码
// POST /user/change-password
func (uc *UserController) ChangePassword(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := uc.getCurrentUserID(c)
	if !ok {
		return
	}

	var req v1.PasswordUpdateReq
	if !uc.bindJSONAndValidate(c, &req) {
		return
	}

	// 调用服务层修改密码
	err := uc.userSvc.ChangePassword(c.Request.Context(), userID, &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, gin.H{"message": "密码修改成功"})
}

// GetNutritionGoal 获取用户营养目标
// GET /user/nutrition-goal
func (uc *UserController) GetNutritionGoal(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := uc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 调用服务层获取营养目标
	res, err := uc.nutritionGoalSvc.GetNutritionGoal(c.Request.Context(), userID)
	if err != nil {
		c.Error(err)
		return
	}

	// 如果营养目标不存在，创建默认营养目标
	if res == nil {
		res, err = uc.nutritionGoalSvc.CreateDefaultNutritionGoal(c.Request.Context(), userID)
		if err != nil {
			c.Error(err)
			return
		}
	}

	response.Success(c, res)
}

// UpdateNutritionGoal 更新用户营养目标
// PUT /user/nutrition-goal
func (uc *UserController) UpdateNutritionGoal(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := uc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 绑定请求参数
	var req v1.NutritionGoalRequestReq
	if !uc.bindJSONAndValidate(c, &req) {
		return
	}

	// 首先获取用户的营养目标ID
	existingGoal, err := uc.nutritionGoalSvc.GetNutritionGoal(c.Request.Context(), userID)
	if err != nil {
		c.Error(err)
		return
	}

	// 如果营养目标不存在，先创建默认营养目标
	if existingGoal == nil {
		existingGoal, err = uc.nutritionGoalSvc.CreateDefaultNutritionGoal(c.Request.Context(), userID)
		if err != nil {
			c.Error(err)
			return
		}
	}

	// 转换为更新请求结构体
	updateReq := &v1.UserNutritionGoalUpdateReq{
		ID:            existingGoal.ID,
		CalorieTarget: req.CalorieTarget,
		WeightTarget:  req.WeightTarget,
		ProteinTarget: req.ProteinTarget,
		CarbsTarget:   req.CarbsTarget,
		FatTarget:     req.FatTarget,
		IsVegetarian:  req.IsVegetarian,
		IsLowCarb:     req.IsLowCarb,
		IsHighProtein: req.IsHighProtein,
		IsGlutenFree:  req.IsGlutenFree,
		IsLowSodium:   req.IsLowSodium,
	}

	// 调用服务层更新营养目标
	res, err := uc.nutritionGoalSvc.UpdateNutritionGoal(c.Request.Context(), updateReq)
	if err != nil {
		c.Error(err)
		return
	}

	// 返回成功标志（与Java版本保持一致）
	response.Success(c, true)
}
