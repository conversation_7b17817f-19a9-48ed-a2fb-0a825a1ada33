package controllers

import (
	"github.com/gin-gonic/gin"
	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/common/response"
	"shikeyinxiang/internal/consts"
	"shikeyinxiang/internal/middleware"
	"shikeyinxiang/internal/service"
)

// UserController 用户端控制器，处理普通用户相关的HTTP请求
type UserController struct {
	userSvc service.IUserService
}

// NewUserController 创建一个新的 UserController 实例
func NewUserController(userSvc service.IUserService) *UserController {
	return &UserController{
		userSvc: userSvc,
	}
}

// bindJSONAndValidate 统一处理JSON绑定和验证
func (uc *UserController) bindJSONAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindJSON(req); err != nil {
		response.Error(c, consts.CodeParameterError)
		return false
	}
	return true
}

// getCurrentUserID 获取当前用户ID
func (uc *UserController) getCurrentUserID(c *gin.Context) (int64, bool) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		response.Error(c, consts.CodeUnauthorized)
		return 0, false
	}
	return userID, true
}

// GetUserInfo 获取当前用户信息
// GET /user/info
func (uc *UserController) GetUserInfo(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := uc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 调用服务层获取用户信息
	res, err := uc.userSvc.GetUserByID(c.Request.Context(), userID)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// UpdateUserInfo 更新用户信息
// PUT /user/update
func (uc *UserController) UpdateUserInfo(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := uc.getCurrentUserID(c)
	if !ok {
		return
	}

	var req v1.UserUpdateReq
	if !uc.bindJSONAndValidate(c, &req) {
		return
	}

	// 设置用户ID
	req.ID = userID

	// 调用服务层更新用户信息
	res, err := uc.userSvc.UpdateUser(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	// 返回更新成功的标志（与Java版本保持一致）
	response.Success(c, true)
}

// GetAvatarUrl 获取用户头像URL
// GET /user/avatar
func (uc *UserController) GetAvatarUrl(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := uc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 调用服务层生成预签名下载URL
	res, err := uc.userSvc.GenerateAvatarDownloadURL(c.Request.Context(), userID)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// GenerateAvatarUploadUrl 生成头像上传URL
// POST /user/avatar/upload-url
func (uc *UserController) GenerateAvatarUploadUrl(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := uc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 获取查询参数中的contentType
	contentType := c.Query("contentType")
	if contentType == "" {
		response.Error(c, consts.CodeParameterError)
		return
	}

	// 调用服务层生成预签名上传URL
	res, err := uc.userSvc.GenerateAvatarUploadURL(c.Request.Context(), userID, contentType)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// UploadAvatar 上传头像（代理上传，解决CORS问题）
// POST /user/avatar
func (uc *UserController) UploadAvatar(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := uc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		response.Error(c, consts.CodeParameterError)
		return
	}
	defer file.Close()

	// 获取文件类型
	contentType := header.Header.Get("Content-Type")
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	// 这里需要调用文件服务的代理上传功能
	// 由于当前用户服务接口中没有直接的代理上传方法，
	// 我们可以先生成上传URL，然后引导用户使用前端上传
	res, err := uc.userSvc.GenerateAvatarUploadURL(c.Request.Context(), userID, contentType)
	if err != nil {
		c.Error(err)
		return
	}

	// 返回上传URL，让前端处理实际上传
	response.Success(c, gin.H{
		"message":   "请使用返回的uploadUrl进行文件上传",
		"uploadUrl": res.UploadUrl,
		"fileName":  res.FileName,
	})
}

// UpdateAvatar 更新用户头像URL（在文件上传完成后调用）
// PUT /user/avatar
func (uc *UserController) UpdateAvatar(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := uc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 绑定请求参数
	var req struct {
		AvatarUrl string `json:"avatarUrl" binding:"required,url"`
	}
	if !uc.bindJSONAndValidate(c, &req) {
		return
	}

	// 调用服务层更新头像URL
	err := uc.userSvc.UpdateUserAvatar(c.Request.Context(), userID, req.AvatarUrl)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, gin.H{"message": "头像更新成功"})
}

// ChangePassword 修改密码
// POST /user/change-password
func (uc *UserController) ChangePassword(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := uc.getCurrentUserID(c)
	if !ok {
		return
	}

	var req v1.PasswordUpdateReq
	if !uc.bindJSONAndValidate(c, &req) {
		return
	}

	// 调用服务层修改密码
	err := uc.userSvc.ChangePassword(c.Request.Context(), userID, &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, gin.H{"message": "密码修改成功"})
}
