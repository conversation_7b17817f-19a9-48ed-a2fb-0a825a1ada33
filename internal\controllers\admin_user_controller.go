package controllers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/common/response"
	"shikeyinxiang/internal/consts"
	"shikeyinxiang/internal/service"
)

// AdminUserController 管理端用户控制器，处理管理员的用户管理HTTP请求
type AdminUserController struct {
	userSvc service.IUserService
}

// NewAdminUserController 创建一个新的 AdminUserController 实例
func NewAdminUserController(userSvc service.IUserService) *AdminUserController {
	return &AdminUserController{
		userSvc: userSvc,
	}
}

// bindJSONAndValidate 统一处理JSON绑定和验证
func (auc *AdminUserController) bindJSONAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindJSON(req); err != nil {
		response.Error(c, consts.CodeParameterError)
		return false
	}
	return true
}

// bindQueryAndValidate 统一处理Query参数绑定和验证
func (auc *AdminUserController) bindQueryAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindQuery(req); err != nil {
		response.Error(c, consts.CodeParameterError)
		return false
	}
	return true
}

// parseIDParam 解析路径参数中的ID
func (auc *AdminUserController) parseIDParam(c *gin.Context, paramName string) (int64, bool) {
	idStr := c.Param(paramName)
	if idStr == "" {
		response.Error(c, consts.CodeParameterError)
		return 0, false
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || id <= 0 {
		response.Error(c, consts.CodeParameterError)
		return 0, false
	}

	return id, true
}

// ListUsers 分页查询用户列表
// GET /api/admin/users
func (auc *AdminUserController) ListUsers(c *gin.Context) {
	// 绑定查询参数
	var req v1.UserQueryReq
	if !auc.bindQueryAndValidate(c, &req) {
		return
	}

	// 设置默认分页参数
	if req.Current <= 0 {
		req.Current = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	// 调用服务层
	res, err := auc.userSvc.GetUserInfoPage(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	// 使用分页响应格式
	response.SuccessWithPage(c, res.Records, res.Total, res.Current, res.Size)
}

// CreateUser 创建用户
// POST /api/admin/users
func (auc *AdminUserController) CreateUser(c *gin.Context) {
	var req v1.UserCreateReq
	if !auc.bindJSONAndValidate(c, &req) {
		return
	}

	// 强制设置角色为普通用户（与Java版本保持一致）
	req.Role = consts.RoleUser

	res, err := auc.userSvc.CreateUser(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// UpdateUser 更新用户信息
// PUT /api/admin/users/{id}
func (auc *AdminUserController) UpdateUser(c *gin.Context) {
	// 获取路径参数中的用户ID
	id, ok := auc.parseIDParam(c, "id")
	if !ok {
		return
	}

	var req v1.UserUpdateReq
	if !auc.bindJSONAndValidate(c, &req) {
		return
	}

	// 设置用户ID
	req.ID = id

	// 调用服务层更新用户
	res, err := auc.userSvc.UpdateUser(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// DeleteUser 删除用户
// DELETE /api/admin/users/{id}
func (auc *AdminUserController) DeleteUser(c *gin.Context) {
	// 获取路径参数中的用户ID
	id, ok := auc.parseIDParam(c, "id")
	if !ok {
		return
	}

	// 调用服务层删除用户
	err := auc.userSvc.DeleteUser(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, gin.H{"message": "用户删除成功"})
}

// UpdateUserStatus 更新用户状态
// PUT /api/admin/users/{id}/status
func (auc *AdminUserController) UpdateUserStatus(c *gin.Context) {
	// 获取路径参数中的用户ID
	id, ok := auc.parseIDParam(c, "id")
	if !ok {
		return
	}

	// 获取查询参数中的状态值
	statusStr := c.Query("status")
	if statusStr == "" {
		response.Error(c, consts.CodeParameterError)
		return
	}

	status, err := strconv.ParseInt(statusStr, 10, 8)
	if err != nil || (status != 0 && status != 1) {
		response.Error(c, consts.CodeParameterError)
		return
	}

	// 构造请求对象
	req := &v1.UserStatusUpdateReq{
		Status: int8(status),
	}

	// 调用服务层更新状态
	err = auc.userSvc.UpdateUserStatus(c.Request.Context(), id, req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, true)
}

// GetUserDetail 获取用户详情
// GET /api/admin/users/{id}
func (auc *AdminUserController) GetUserDetail(c *gin.Context) {
	// 获取路径参数中的用户ID
	id, ok := auc.parseIDParam(c, "id")
	if !ok {
		return
	}

	// 调用服务层获取用户详情
	res, err := auc.userSvc.GetUserByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}
